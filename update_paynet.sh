#!/bin/bash

# Script to update Paynet module files on server
# This script fixes the URL redirection issue

echo "🔄 Updating Paynet module files on server..."

# Check if we're running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run as root"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "include/php/PaynetAPI.php" ]; then
    echo "❌ Please run this script from the module directory"
    exit 1
fi

echo "📁 Copying updated files..."

# Copy updated PaynetAPI.php
cp include/php/PaynetAPI.php /usr/local/mgr5/include/php/
echo "✅ Updated PaynetAPI.php"

# Copy updated payment script
cp cgi/paynet_payment.php /usr/local/mgr5/sbin/mancgi/
chmod +x /usr/local/mgr5/sbin/mancgi/paynet_payment.php
echo "✅ Updated paynet_payment.php"

# Copy updated result script
cp cgi/paynet_result.php /usr/local/mgr5/sbin/mancgi/
chmod +x /usr/local/mgr5/sbin/mancgi/paynet_result.php
echo "✅ Updated paynet_result.php"

# Copy updated XML configuration
cp etc/xml/billmgr_mod_pmpaynet.php.xml /usr/local/mgr5/etc/xml/
echo "✅ Updated XML configuration"

echo "🔄 Clearing BILLmanager cache..."
rm -rf /usr/local/mgr5/var/cache/* /usr/local/mgr5/var/tmp/* 2>/dev/null || true

echo "🔄 Restarting BILLmanager..."
/usr/local/mgr5/sbin/mgrctl exit -m billmgr 2>/dev/null || true

echo "⏳ Waiting for BILLmanager to start (15 seconds)..."
sleep 15

echo "🔍 Checking BILLmanager status..."
if pgrep -f "bin/core billmgr" > /dev/null; then
    echo "✅ BILLmanager is running"
else
    echo "⚠️  BILLmanager may still be starting..."
fi

echo ""
echo "✅ Update completed!"
echo ""
echo "🔧 Changes made:"
echo "• Added test mode support to PaynetAPI.php"
echo "• Fixed URL generation to use correct Paynet endpoints"
echo "• Updated payment and result scripts to handle test mode"
echo "• Production URLs: paynet.md"
echo "• Test URLs: test.paynet.md"
echo ""
echo "📝 Next steps:"
echo "1. Go to BILLmanager admin panel"
echo "2. Edit your Paynet payment method"
echo "3. Check/uncheck 'Test Mode' as needed"
echo "4. Test a payment to verify correct URL redirection"
echo ""
echo "🐛 If you still see paynet.example.com:"
echo "1. Check the merchant credentials are correct"
echo "2. Verify test mode setting"
echo "3. Check logs: tail -f /usr/local/mgr5/var/pmpaynet.log"
