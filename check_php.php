#!/usr/bin/php
<?php
echo "Проверка PHP конфигурации для модуля Paynet\n";
echo "==========================================\n\n";

// Проверяем версию PHP
echo "PHP версия: " . PHP_VERSION . "\n";

// Проверяем необходимые расширения
$required_extensions = ['xml', 'simplexml', 'curl', 'json'];
$missing_extensions = [];

foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✓ Расширение $ext: установлено\n";
    } else {
        echo "✗ Расширение $ext: НЕ УСТАНОВЛЕНО\n";
        $missing_extensions[] = $ext;
    }
}

echo "\n";

// Проверяем CLI режим
if (php_sapi_name() === 'cli') {
    echo "✓ PHP CLI: работает\n";
} else {
    echo "✗ PHP CLI: не работает\n";
}

// Проверяем возможность создания XML
try {
    $xml = simplexml_load_string('<?xml version="1.0" encoding="UTF-8"?><doc/>');
    if ($xml !== false) {
        echo "✓ XML обработка: работает\n";
    } else {
        echo "✗ XML обработка: не работает\n";
    }
} catch (Exception $e) {
    echo "✗ XML обработка: ошибка - " . $e->getMessage() . "\n";
}

echo "\n";

if (empty($missing_extensions)) {
    echo "✓ Все необходимые компоненты установлены!\n";
    echo "Модуль Paynet должен работать корректно.\n";
} else {
    echo "✗ Отсутствуют необходимые компоненты:\n";
    foreach ($missing_extensions as $ext) {
        echo "  - $ext\n";
    }
    echo "\nДля установки выполните:\n";
    echo "CentOS/RHEL: sudo yum install -y php-cli php-common php-xml\n";
    echo "Ubuntu/Debian: sudo apt-get install -y php-cli php-common php-xml\n";
}

echo "\n";
?>
