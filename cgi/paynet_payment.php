#!/usr/bin/php
<?php
set_include_path(get_include_path() . PATH_SEPARATOR . '/usr/local/mgr5/include/php');
define('__MODULE__', 'pmpaynet');
require_once 'bill_func_paynet.php';

echo "Content-Type: text/html; charset=utf-8\n\n";

$input = CgiInput();

if (empty($input['elid'])) {
	throw new Exception('Empty elid');
}

$info = LocalQuery('payment.info', ['elid' => $input['elid']]);
if (empty($info['payment'][0])) {
	throw new Exception('Empty info');
}

$payment = $info['payment'][0];

$paymethod = (isset($payment['paymethod'][1]) ? $payment['paymethod'][1] : $payment['paymethod']);
$merchant_id = $paymethod['merchant_id']['$'];
$secret_1 = $paymethod['secret_1']['$'];

$order_id = $payment['id']['$'];
$amount = $payment['paymethodamount']['$'];
$currency = $payment['currency'][1]['iso']['$'];
$desc = $payment['project']['name']['$'] . ' #' . $order_id;
$lang = _get_locale_lang("billmgrlang5");
if (is_null($lang)) {
    $lang = _get_locale_lang("billmgrlang6");
}
$lang = (in_array($lang, ['ru', 'en'])) ? $lang : 'ru';
$sign = hash('sha256', implode(':', [$merchant_id, $amount, $currency, $secret_1, $order_id]));

echo '
<!DOCTYPE html>
	<html lang="ru">
		<head>
			<title>Redirect to Paynet</title>
			<meta charset="UTF-8">
		</head>
		<body>
		<form name="payment_paynet" method="GET" action="https://paynet.example.com/merchant/pay">
			<input type="hidden" name="merchant_id" value="' . $merchant_id . '">
			<input type="hidden" name="amount" value="' . $amount . '">
			<input type="hidden" name="currency" value="' . $currency . '">
			<input type="hidden" name="order_id" value="' . $order_id . '">
			<input type="hidden" name="sign" value="' . $sign . '">
			<input type="hidden" name="desc" value="' . $desc . '">
			<input type="hidden" name="lang" value="' . $lang . '">
		</form>
		<script>
			document.payment_paynet.submit();
		</script>
	</body>
</html>
';
?>