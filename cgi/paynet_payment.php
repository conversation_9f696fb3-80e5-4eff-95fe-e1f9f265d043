#!/usr/bin/php
<?php
set_include_path(get_include_path() . PATH_SEPARATOR . '/usr/local/mgr5/include/php');
define('__MODULE__', 'pmpaynet');
require_once 'bill_func_paynet.php';
require_once 'PaynetAPI.php';

echo "Content-Type: text/html; charset=utf-8\n\n";

$input = CgiInput();

if (empty($input['elid'])) {
	throw new Exception('Empty elid');
}

$info = LocalQuery('payment.info', ['elid' => $input['elid']]);
if (empty($info['payment'][0])) {
	throw new Exception('Empty info');
}

$payment = $info['payment'][0];

$paymethod = (isset($payment['paymethod'][1]) ? $payment['paymethod'][1] : $payment['paymethod']);

// Проверяем наличие всех необходимых полей
$required_fields = ['merchant_code', 'merchant_secret_key', 'merchant_user', 'merchant_user_password'];
foreach ($required_fields as $field) {
    if (!isset($paymethod[$field]['$']) || empty($paymethod[$field]['$'])) {
        Error("Missing required field: $field");
        throw new Exception("Payment method configuration incomplete. Missing field: $field");
    }
}

$merchant_code = $paymethod['merchant_code']['$'];
$merchant_secret_key = $paymethod['merchant_secret_key']['$'];
$merchant_user = $paymethod['merchant_user']['$'];
$merchant_user_password = $paymethod['merchant_user_password']['$'];
$test_mode = isset($paymethod['test_mode']['$']) && $paymethod['test_mode']['$'] === 'on';

$order_id = $payment['id']['$'];
$amount = intval(floatval($payment['paymethodamount']['$']) * 100); // Convert to kopecks
$currency = $payment['currency'][1]['iso']['$'];
$desc = $payment['project']['name']['$'] . ' #' . $order_id;
$lang = _get_locale_lang("billmgrlang5");
if (is_null($lang)) {
    $lang = _get_locale_lang("billmgrlang6");
}
$lang = (in_array($lang, ['ru', 'en', 'ro'])) ? $lang : 'ru';

Debug('Paynet payment for order: ' . $order_id . ', test_mode: ' . ($test_mode ? 'true' : 'false'));

// Create Paynet API instance
$api = new PaynetEcomAPI($merchant_code, $merchant_secret_key, $merchant_user, $merchant_user_password, $test_mode);

// Create payment request
$prequest = new PaynetRequest();
$prequest->ExternalID = $order_id;
$prequest->LinkSuccess = "http://" . $_SERVER['HTTP_HOST'] . "/mancgi/paynet_success.php?elid=" . $order_id;
$prequest->LinkCancel = "http://" . $_SERVER['HTTP_HOST'] . "/mancgi/paynet_cancel.php?elid=" . $order_id;
$prequest->Lang = $lang;
$prequest->Amount = $amount;

// Set up products
$prequest->Products = array(
    array(
        'LineNo' => '1',
        'Code' => 'payment_' . $order_id,
        'Barcode' => $order_id,
        'Name' => $desc,
        'Descrption' => $desc,
        'Quantity' => 100, // 100 = 1.00
        'UnitPrice' => $amount
    )
);

// Set up service
$prequest->Service = array(
    'Name' => 'BillManager Payment',
    'Description' => $desc,
    'Amount' => $amount,
    'Products' => $prequest->Products
);

// Set up customer
$prequest->Customer = array(
    'Code' => $payment['account']['$'] ?? 'customer_' . $order_id,
    'Address' => $_SERVER['HTTP_HOST'],
    'Name' => $payment['account']['$'] ?? 'Customer'
);

try {
    $formObj = $api->FormCreate($prequest);

    if ($formObj->IsOk()) {
        Debug('Paynet form created successfully for order: ' . $order_id);
        echo $formObj->Data;
    } else {
        Error('Paynet form creation failed: ' . $formObj->Message);
        throw new Exception('Payment form creation failed: ' . $formObj->Message);
    }
} catch (Exception $e) {
    Error('Paynet payment error: ' . $e->getMessage());
    echo '
<!DOCTYPE html>
<html>
<head>
    <title>Payment Error</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>Payment Error</h1>
    <p>Unable to process payment. Please try again later.</p>
    <p>Error: ' . htmlspecialchars($e->getMessage()) . '</p>
</body>
</html>
';
}
?>