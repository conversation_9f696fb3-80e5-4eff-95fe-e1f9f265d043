#!/usr/bin/php
<?php
set_include_path(get_include_path() . PATH_SEPARATOR . '/usr/local/mgr5/include/php');
define('__MODULE__', 'pmpaynet');
require_once 'bill_func_paynet.php';

echo "Content-Type: text/html; charset=utf-8\n\n";

$input = CgiInput();

if (empty($input['elid'])) {
    Debug('Cancel page: Empty elid');
    echo '<!DOCTYPE html>
<html>
<head>
    <title>Payment Cancelled</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>Payment Cancelled</h1>
    <p>Invalid payment reference.</p>
</body>
</html>';
    exit;
}

$order_id = $input['elid'];
Debug('Cancel page accessed for order: ' . $order_id);

// Get payment info
$info = LocalQuery('payment.info', ['elid' => $order_id]);
if (empty($info['payment'][0])) {
    Debug('Cancel page: Order not found: ' . $order_id);
}

echo '<!DOCTYPE html>
<html>
<head>
    <title>Payment Cancelled</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .cancelled { color: #dc3545; }
        .container { max-width: 600px; margin: 0 auto; text-align: center; }
        .btn { 
            display: inline-block; 
            padding: 10px 20px; 
            background-color: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="cancelled">Payment Cancelled</h1>
        <p>Your payment has been cancelled.</p>
        <p><strong>Order ID:</strong> ' . htmlspecialchars($order_id) . '</p>
        <p>You can try again or contact support if you need assistance.</p>
        <a href="javascript:history.back()" class="btn">Go Back</a>
    </div>
</body>
</html>';
?>
