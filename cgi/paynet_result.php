#!/usr/bin/php
<?php
set_include_path(get_include_path() . PATH_SEPARATOR . "/usr/local/mgr5/include/php");
define('__MODULE__', "pmpaynet");
require_once 'bill_func_paynet.php';
require_once 'PaynetAPI.php';

echo "Content-Type: text/html; charset=utf-8\n\n";

Debug('Вошли в файл paynet_result.php');

// Get JSON payload from Paynet callback
$paymentInfo = file_get_contents('php://input');
Debug('Received callback data: ' . $paymentInfo);

$paymentObj = json_decode($paymentInfo);
if (!$paymentObj) {
    Debug("Invalid JSON payload received");
    die("Invalid JSON payload");
}

// Check if this is a PAID event
if (!isset($paymentObj->EventType) || $paymentObj->EventType !== 'PAID') {
    Debug("Event type is not PAID: " . ($paymentObj->EventType ?? 'unknown'));
    die("Event type not supported");
}

// Get external ID (order ID)
if (!isset($paymentObj->Payment->ExternalId)) {
    Debug("ExternalId not found in callback");
    die("ExternalId missing");
}

$order_id = $paymentObj->Payment->ExternalId;
Debug('Processing payment for order: ' . $order_id);

// Get payment info from BillManager
$info = LocalQuery('payment.info', ['elid' => $order_id]);
if (empty($info['payment'][0])) {
    Debug("Order '" . $order_id . "' not found in BM");
    die("Order not found");
}

$payment = $info['payment'][0];
$paymethod = (isset($payment['paymethod'][1]) ? $payment['paymethod'][1] : $payment['paymethod']);

// Get Paynet credentials
$merchant_code = $paymethod['merchant_code']['$'];
$merchant_secret_key = $paymethod['merchant_secret_key']['$'];
$merchant_user = $paymethod['merchant_user']['$'];
$merchant_user_password = $paymethod['merchant_user_password']['$'];
$test_mode = isset($paymethod['test_mode']['$']) && $paymethod['test_mode']['$'] === 'on';

Debug('Paynet result verification for order: ' . $order_id . ', test_mode: ' . ($test_mode ? 'true' : 'false'));

// Create Paynet API instance to verify payment
$api = new PaynetEcomAPI($merchant_code, $merchant_secret_key, $merchant_user, $merchant_user_password, $test_mode);

try {
    // Verify payment status with Paynet
    $checkObj = $api->PaymentGet($order_id);

    if (!$checkObj->IsOk()) {
        Debug('Failed to verify payment with Paynet: ' . $checkObj->Message);
        die('Payment verification failed');
    }

    // Check if payment is actually completed (Status = 4)
    if (!isset($checkObj->Data[0]['Status']) || $checkObj->Data[0]['Status'] !== 4) {
        Debug('Payment status is not complete. Status: ' . ($checkObj->Data[0]['Status'] ?? 'unknown'));
        die('Payment not completed');
    }

    // Verify amounts match
    $bm_amount = intval(floatval($payment['paymethodamount']['$']) * 100);
    $paynet_amount = $checkObj->Data[0]['Amount'] ?? 0;

    if ($bm_amount != $paynet_amount) {
        Debug("Amount mismatch. BM: $bm_amount, Paynet: $paynet_amount");
        die('Amount mismatch');
    }

    // Mark payment as paid in BillManager
    LocalQuery('payment.setpaid', [
        'elid' => $order_id,
        'info' => 'Paynet payment completed. Transaction data: ' . $paymentInfo
    ]);

    Debug("Order '" . $order_id . "' successfully marked as paid");
    die('OK');

} catch (Exception $e) {
    Error('Paynet callback error: ' . $e->getMessage());
    die('Internal error');
}
?>