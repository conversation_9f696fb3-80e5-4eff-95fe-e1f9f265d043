#!/usr/bin/php
<?php
set_include_path(get_include_path() . PATH_SEPARATOR . "/usr/local/mgr5/include/php");
define('__MODULE__', "pmaaio");
require_once 'bill_func_aaio.php';

echo "Content-Type: text/html; charset=utf-8\n\n";

Debug('Вошли в файл aaio_result.php');

$ctx = stream_context_create([
	'http' => [
        'timeout' => 10
    ]
]);

$ips = json_decode(file_get_contents('https://aaio.so/api/public/ips', false, $ctx));
$ip = getIP();

if (isset($ips->list) && !in_array($ip, $ips->list)) {
	Debug("Unknown IP " . $ip);
	die("Unknown IP " . $ip);
}

$params = CgiInput(true);
$check_params = ['merchant_id', 'amount', 'currency', 'order_id', 'sign'];

foreach($check_params as $check_param) {
	if(empty($params[$check_param])) {
		Debug("Empty param '" . $check_param . "'");
		die("Empty params '" . $check_param . "'");
	}
}

$info = LocalQuery('payment.info', ['elid' => $params['order_id']]);
if(empty($info['payment'][0])) {
	Debug("Order '" . $params['order_id'] . "' not found in BM");
	die("Order '" . $params['order_id'] . "' not found in BM");
}

$payment = $info['payment'][0];
$paymethod = (isset($payment['paymethod'][1]) ? $payment['paymethod'][1] : $payment['paymethod']);
$amount = $payment['paymethodamount']['$'];
$currency_code = $payment['currency'][1]['iso']['$'];
$secret_2 = $paymethod['secret_2']['$'];
	
if($amount != $params['amount']) {
	Debug("Amount does not match. Order '" . $params['order_id'] . "'");
	die("Amount does not match. Order '" . $params['order_id'] . "'");
}
		
if($currency_code != $params['currency']) {
	Debug("The currency is incorrect. Order '" . $params['order_id'] . "'");
	die("The currency is incorrect. Order '" . $params['order_id'] . "'");
}

$sign = hash('sha256', implode(':', [$params['merchant_id'], $params['amount'], $params['currency'], $secret_2, $params['order_id']]));

if (!hash_equals($params['sign'], $sign)) {
	Debug("Hash signature does not match. Order '" . $params['order_id'] . "'");
	die("Hash signature does not match. Order '" . $params['order_id'] . "'");
}
	
LocalQuery('payment.setpaid', ['elid' => $params['order_id'], 'info' => print_r($params, true)]);
Debug("Order '" . $params['order_id'] . "' is paid");
		
die('OK');
?>