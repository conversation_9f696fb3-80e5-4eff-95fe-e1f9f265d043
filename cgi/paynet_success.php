#!/usr/bin/php
<?php
set_include_path(get_include_path() . PATH_SEPARATOR . '/usr/local/mgr5/include/php');
define('__MODULE__', 'pmpaynet');
require_once 'bill_func_paynet.php';
require_once 'PaynetAPI.php';

echo "Content-Type: text/html; charset=utf-8\n\n";

$input = CgiInput();

if (empty($input['elid'])) {
    Debug('Success page: Empty elid');
    echo '<!DOCTYPE html>
<html>
<head>
    <title>Payment Status</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>Payment Error</h1>
    <p>Invalid payment reference.</p>
</body>
</html>';
    exit;
}

$order_id = $input['elid'];
Debug('Success page accessed for order: ' . $order_id);

// Get payment info
$info = LocalQuery('payment.info', ['elid' => $order_id]);
if (empty($info['payment'][0])) {
    Debug('Success page: Order not found: ' . $order_id);
    echo '<!DOCTYPE html>
<html>
<head>
    <title>Payment Status</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>Payment Error</h1>
    <p>Payment not found.</p>
</body>
</html>';
    exit;
}

$payment = $info['payment'][0];
$status = $payment['status']['$'] ?? '';

echo '<!DOCTYPE html>
<html>
<head>
    <title>Payment Success</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: #28a745; }
        .pending { color: #ffc107; }
        .container { max-width: 600px; margin: 0 auto; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="success">Payment Processed</h1>
        <p>Thank you for your payment!</p>
        <p><strong>Order ID:</strong> ' . htmlspecialchars($order_id) . '</p>
        <p><strong>Amount:</strong> ' . htmlspecialchars($payment['paymethodamount']['$']) . ' ' . htmlspecialchars($payment['currency'][1]['iso']['$']) . '</p>';

if ($status === 'paid') {
    echo '<p class="success"><strong>Status:</strong> Payment Confirmed</p>';
} else {
    echo '<p class="pending"><strong>Status:</strong> Payment Processing</p>
          <p>Your payment is being processed. You will receive confirmation shortly.</p>';
}

echo '    </div>
</body>
</html>';
?>
