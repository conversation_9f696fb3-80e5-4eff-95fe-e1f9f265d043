#!/bin/bash

echo "Исправление проблемы с формой настройки Paynet"
echo "============================================="
echo ""

if [ "$EUID" -ne 0 ]; then
    echo "❌ Запустите скрипт от имени root: sudo ./fix_form_issue.sh"
    exit 1
fi

MGR_PATH="/usr/local/mgr5"

echo "1. Проверка текущего XML файла..."
if [ -f "$MGR_PATH/etc/xml/billmgr_mod_pmpaynet.php.xml" ]; then
    echo "✓ XML файл существует"
    
    # Проверяем валидность XML
    if xmllint --noout "$MGR_PATH/etc/xml/billmgr_mod_pmpaynet.php.xml" 2>/dev/null; then
        echo "✓ XML файл валиден"
    else
        echo "❌ XML файл невалиден"
        echo "Заменяем на упрощенную версию..."
        cp billmgr_mod_pmpaynet_simple.php.xml "$MGR_PATH/etc/xml/billmgr_mod_pmpaynet.php.xml"
    fi
else
    echo "❌ XML файл не найден, копируем..."
    cp billmgr_mod_pmpaynet_simple.php.xml "$MGR_PATH/etc/xml/billmgr_mod_pmpaynet.php.xml"
fi

echo ""
echo "2. Установка правильных прав доступа..."
chown root:root "$MGR_PATH/etc/xml/billmgr_mod_pmpaynet.php.xml"
chmod 644 "$MGR_PATH/etc/xml/billmgr_mod_pmpaynet.php.xml"

echo ""
echo "3. Очистка кеша BILLmanager..."
if [ -d "$MGR_PATH/var/cache" ]; then
    rm -rf "$MGR_PATH/var/cache"/*
    echo "✓ Кеш очищен"
fi

echo ""
echo "4. Проверка модуля..."
if [ -f "$MGR_PATH/addon/pmpaynet.php" ]; then
    if "$MGR_PATH/addon/pmpaynet.php" --command=config >/dev/null 2>&1; then
        echo "✓ Модуль работает"
    else
        echo "❌ Модуль не работает"
    fi
else
    echo "❌ Модуль не найден"
fi

echo ""
echo "5. Перезапуск BILLmanager..."
if command -v systemctl >/dev/null 2>&1; then
    systemctl restart billmgr
elif [ -f "$MGR_PATH/sbin/mgrctl" ]; then
    "$MGR_PATH/sbin/mgrctl" -m billmgr restart
else
    echo "⚠ Не удалось найти команду перезапуска BILLmanager"
    echo "Попробуйте вручную: systemctl restart billmgr"
fi

echo ""
echo "6. Ожидание запуска..."
sleep 10

echo ""
echo "✅ Исправление завершено!"
echo ""
echo "Теперь:"
echo "1. Зайдите в веб-интерфейс BILLmanager"
echo "2. Очистите кеш браузера (Ctrl+F5)"
echo "3. Перейдите в 'Методы оплаты' → 'Добавить'"
echo "4. Выберите 'Paynet' из списка"
echo ""
echo "Если форма все еще не отображается:"
echo "- Проверьте логи: tail -f $MGR_PATH/var/billmgr.log"
echo "- Убедитесь, что BILLmanager запущен: ps aux | grep billmgr"
