#!/bin/bash

echo "Исправление модуля Paynet согласно официальной документации"
echo "=========================================================="
echo ""

if [ "$EUID" -ne 0 ]; then
    echo "❌ Запустите от root: sudo ./fix_according_to_docs.sh"
    exit 1
fi

MGR_PATH="/usr/local/mgr5"

echo "1. Создание правильного XML согласно документации..."
cat > "$MGR_PATH/etc/xml/billmgr_mod_pmpaynet.php.xml" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<mgrdata>
    <plugin name="pmpaynet.php">
        <group>payment_method</group>
        <msg name="desc_short" lang="ru">Paynet</msg>
        <msg name="desc_short" lang="en">Paynet</msg>
        <msg name="desc_full" lang="ru">Платежный модуль Paynet</msg>
        <msg name="desc_full" lang="en">Payment module Paynet</msg>
    </plugin>

    <metadata name="paymethod.edit.paynet.php" type="form">
        <form>
            <page name="methodprops">
                <field name="merchant_code">
                    <input name="merchant_code" required="yes" type="text"/>
                </field>
                <field name="merchant_secret_key">
                    <input name="merchant_secret_key" private="yes" required="yes" type="text"/>
                </field>
                <field name="merchant_user">
                    <input name="merchant_user" required="yes" type="text"/>
                </field>
                <field name="merchant_user_password">
                    <input name="merchant_user_password" private="yes" required="yes" type="text"/>
                </field>
                <field name="test_mode">
                    <input name="test_mode" type="checkbox"/>
                </field>
            </page>
        </form>
    </metadata>

    <lang name="ru">
        <messages name="label_paymethod">
            <msg name="pmpaynet.php">Paynet</msg>
            <msg name="module_pmpaynet.php">Paynet</msg>
        </messages>
        <messages name="paymethod.edit.paynet.php">
            <msg name="merchant_code">Код мерчанта</msg>
            <msg name="hint_merchant_code">Код мерчанта в системе Paynet</msg>
            <msg name="merchant_secret_key">Секретный ключ</msg>
            <msg name="hint_merchant_secret_key">Секретный ключ мерчанта</msg>
            <msg name="merchant_user">Пользователь</msg>
            <msg name="hint_merchant_user">Пользователь API</msg>
            <msg name="merchant_user_password">Пароль</msg>
            <msg name="hint_merchant_user_password">Пароль пользователя API</msg>
            <msg name="test_mode">Тестовый режим</msg>
            <msg name="hint_test_mode">Включить тестовый режим</msg>
        </messages>
    </lang>

    <lang name="en">
        <messages name="label_paymethod">
            <msg name="pmpaynet.php">Paynet</msg>
            <msg name="module_pmpaynet.php">Paynet</msg>
        </messages>
        <messages name="paymethod.edit.paynet.php">
            <msg name="merchant_code">Merchant Code</msg>
            <msg name="hint_merchant_code">Merchant code in Paynet system</msg>
            <msg name="merchant_secret_key">Secret Key</msg>
            <msg name="hint_merchant_secret_key">Merchant secret key</msg>
            <msg name="merchant_user">User</msg>
            <msg name="hint_merchant_user">API user</msg>
            <msg name="merchant_user_password">Password</msg>
            <msg name="hint_merchant_user_password">API user password</msg>
            <msg name="test_mode">Test Mode</msg>
            <msg name="hint_test_mode">Enable test mode</msg>
        </messages>
    </lang>
</mgrdata>
EOF

echo "✓ XML создан согласно документации"

echo ""
echo "2. Установка правильных прав доступа..."
chmod 644 "$MGR_PATH/etc/xml/billmgr_mod_pmpaynet.php.xml"
chmod 755 "$MGR_PATH/addon/pmpaynet.php"
chmod 755 "$MGR_PATH/sbin/mancgi/paynet_payment.php" 2>/dev/null
chmod 755 "$MGR_PATH/sbin/mancgi/paynet_result.php" 2>/dev/null
chmod 755 "$MGR_PATH/sbin/mancgi/paynet_success.php" 2>/dev/null
chmod 755 "$MGR_PATH/sbin/mancgi/paynet_cancel.php" 2>/dev/null
chmod 644 "$MGR_PATH/include/php/bill_func_paynet.php"
chmod 644 "$MGR_PATH/include/php/PaynetAPI.php"

echo ""
echo "3. Очистка кеша..."
rm -rf "$MGR_PATH/var/cache"/* 2>/dev/null
rm -rf "$MGR_PATH/var/tmp"/* 2>/dev/null

echo ""
echo "4. Правильный перезапуск BILLmanager согласно документации..."
"$MGR_PATH/sbin/mgrctl" exit -m billmgr

echo ""
echo "5. Ожидание перезапуска..."
sleep 15

echo ""
echo "✅ Исправление завершено!"
echo ""
echo "Теперь:"
echo "1. Обновите страницу в браузере (Ctrl+F5)"
echo "2. Перейдите в 'Методы оплаты' → 'Добавить'"
echo "3. Выберите 'Paynet' из списка"
echo "4. Форма настройки должна отображаться"
echo ""
echo "Если проблемы остались:"
echo "- Проверьте логи: tail -f $MGR_PATH/var/billmgr.log"
echo "- Убедитесь что BILLmanager запустился: ps aux | grep billmgr"
