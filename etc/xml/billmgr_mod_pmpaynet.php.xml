<?xml version="1.0" encoding="UTF-8"?>
<mgrdata>
<plugin name="pmpaynet.php">
<group>payment_method</group>
<msg name="desc_short" lang="ru">Paynet</msg>
<msg name="desc_short" lang="en">Paynet</msg>
<msg name="desc_full" lang="ru">Платежный модуль Paynet</msg>
<msg name="desc_full" lang="en">Payment module Paynet</msg>
</plugin>

<metadata name="paymethod.edit.paynet.php" type="form">
<form>
<page name="methodprops">
<field name="merchant_code">
<input name="merchant_code" required="yes" type="text"/>
</field>
<field name="merchant_secret_key">
<input name="merchant_secret_key" private="yes" required="yes" type="text"/>
</field>
<field name="merchant_user">
<input name="merchant_user" required="yes" type="text"/>
</field>
<field name="merchant_user_password">
<input name="merchant_user_password" private="yes" required="yes" type="text"/>
</field>
<field name="test_mode">
<input name="test_mode" type="checkbox"/>
</field>
</page>
</form>
</metadata>

<lang name="ru">
<messages name="label_paymethod">
<msg name="pmpaynet.php">Paynet</msg>
<msg name="module_pmpaynet.php">Paynet</msg>
</messages>
<messages name="paymethod.edit.paynet.php">
<msg name="merchant_code">Код мерчанта</msg>
<msg name="merchant_secret_key">Секретный ключ</msg>
<msg name="merchant_user">Пользователь API</msg>
<msg name="merchant_user_password">Пароль API</msg>
<msg name="test_mode">Тестовый режим</msg>
</messages>
</lang>

<lang name="en">
<messages name="label_paymethod">
<msg name="pmpaynet.php">Paynet</msg>
<msg name="module_pmpaynet.php">Paynet</msg>
</messages>
<messages name="paymethod.edit.paynet.php">
<msg name="merchant_code">Merchant Code</msg>
<msg name="merchant_secret_key">Secret Key</msg>
<msg name="merchant_user">API User</msg>
<msg name="merchant_user_password">API Password</msg>
<msg name="test_mode">Test Mode</msg>
</messages>
</lang>
</mgrdata>
