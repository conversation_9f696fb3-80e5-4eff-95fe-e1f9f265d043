#!/usr/bin/php
<?php
set_include_path(get_include_path() . PATH_SEPARATOR . '/usr/local/mgr5/include/php');
define('__MODULE__', 'pmaaio');
require_once 'bill_func_aaio.php';

$longopts  = [
	'command:',
	'payment:',
	'amount:',
	'recurring:',
];
$options = getopt('', $longopts);

$command = $options['command'];

if ($command === 'config') {
	$config_xml = simplexml_load_string($default_xml_string);

	$feature_node = $config_xml->addChild('feature');
	$feature_node->addChild('redirect', 'on');
	$feature_node->addChild('notneedprofile', 'on');

	$param_node = $config_xml->addChild('param');
	$param_node->addChild('payment_script', '/mancgi/aaio_payment.php');

	echo $config_xml->asXML();
} else {
	throw new Exception('unknown command');
}

?>