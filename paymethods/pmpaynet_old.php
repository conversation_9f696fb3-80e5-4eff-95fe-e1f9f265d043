#!/usr/bin/php -q
<?php
// Disable all error output to prevent XML corruption
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 0);
ini_set('html_errors', 0);

// Capture and discard any output that might interfere with XML
ob_start();

set_include_path(get_include_path() . PATH_SEPARATOR . '/usr/local/mgr5/include/php');
define('__MODULE__', 'pmpaynet');

// Define default XML string
$default_xml_string = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<doc/>\n";

// Try to include bill_func_paynet.php from different possible locations
if (file_exists('/usr/local/mgr5/include/php/bill_func_paynet.php')) {
    require_once '/usr/local/mgr5/include/php/bill_func_paynet.php';
} elseif (file_exists('bill_func_paynet.php')) {
    require_once 'bill_func_paynet.php';
} elseif (file_exists('../include/php/bill_func_paynet.php')) {
    require_once '../include/php/bill_func_paynet.php';
}

// Define minimal functions if not loaded
if (!function_exists('Debug')) {
    function Debug($str) { /* no-op */ }
}
if (!function_exists('Error')) {
    function Error($str) { /* no-op */ }
}

try {
    $longopts  = [
        'command:',
        'payment:',
        'amount:',
        'recurring:',
    ];
    $options = getopt('', $longopts);

    $command = isset($options['command']) ? $options['command'] : '';

    switch ($command) {
        case 'config':
            $config_xml = simplexml_load_string($default_xml_string);

            $feature_node = $config_xml->addChild('feature');
            $feature_node->addChild('redirect', 'on');
            $feature_node->addChild('notneedprofile', 'on');

            $param_node = $config_xml->addChild('param');
            $param_node->addChild('payment_script', '/mancgi/paynet_payment.php');
            $param_node->addChild('result_script', '/mancgi/paynet_result.php');

            // Clear any captured output and send clean XML
            ob_clean();
            echo $config_xml->asXML();
            break;

        case 'payment':
            // Handle payment processing
            if (!isset($options['payment']) || !isset($options['amount'])) {
                ob_clean();
                echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<doc><error>Missing payment parameters</error></doc>";
                exit(1);
            }

            // For now, just return success - actual payment processing is handled by payment_script
            ob_clean();
            echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<doc><result>ok</result></doc>";
            break;

        case 'recurring':
            // Handle recurring payments if needed
            ob_clean();
            echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<doc><result>not_supported</result></doc>";
            break;

        default:
            ob_clean();
            echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<doc><error>unknown command: " . htmlspecialchars($command) . "</error></doc>";
            exit(1);
    }
} catch (Exception $e) {
    ob_clean();
    echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<doc><error>" . htmlspecialchars($e->getMessage()) . "</error></doc>";
    exit(1);
}

?>