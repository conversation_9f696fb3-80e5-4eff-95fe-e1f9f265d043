#!/usr/bin/php
<?php
set_include_path(get_include_path() . PATH_SEPARATOR . '/usr/local/mgr5/include/php');
define('__MODULE__', 'pmpaynet');

// Try to include bill_func_paynet.php from different possible locations
if (file_exists('/usr/local/mgr5/include/php/bill_func_paynet.php')) {
    require_once '/usr/local/mgr5/include/php/bill_func_paynet.php';
} elseif (file_exists('bill_func_paynet.php')) {
    require_once 'bill_func_paynet.php';
} elseif (file_exists('../include/php/bill_func_paynet.php')) {
    require_once '../include/php/bill_func_paynet.php';
} else {
    // Fallback - define minimal required variables
    $default_xml_string = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<doc/>\n";
}

$longopts  = [
	'command:',
	'payment:',
	'amount:',
	'recurring:',
];
$options = getopt('', $longopts);

$command = isset($options['command']) ? $options['command'] : '';

switch ($command) {
	case 'config':
		// Ensure $default_xml_string is defined
		if (!isset($default_xml_string)) {
			$default_xml_string = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<doc/>\n";
		}

		$config_xml = simplexml_load_string($default_xml_string);

		$feature_node = $config_xml->addChild('feature');
		$feature_node->addChild('redirect', 'on');
		$feature_node->addChild('notneedprofile', 'on');

		$param_node = $config_xml->addChild('param');
		$param_node->addChild('payment_script', '/mancgi/paynet_payment.php');
		$param_node->addChild('result_script', '/mancgi/paynet_result.php');

		echo $config_xml->asXML();
		break;

	case 'payment':
		// Handle payment processing
		if (!isset($options['payment']) || !isset($options['amount'])) {
			throw new Exception('Missing payment parameters');
		}

		$payment_id = $options['payment'];
		$amount = $options['amount'];

		// For now, just return success - actual payment processing is handled by payment_script
		echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<doc><result>ok</result></doc>";
		break;

	case 'recurring':
		// Handle recurring payments if needed
		echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<doc><result>not_supported</result></doc>";
		break;

	default:
		throw new Exception('unknown command: ' . $command);
}

?>