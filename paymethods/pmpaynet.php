#!/usr/bin/php
<?php
// Clean version of Paynet module for BILLmanager 6
// This version avoids any potential issues with dependencies

// Disable all error output
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 0);

// Parse command line arguments
$longopts = [
    'command:',
    'payment:',
    'amount:',
    'recurring:',
];
$options = getopt('', $longopts);
$command = isset($options['command']) ? $options['command'] : '';

// Handle commands
switch ($command) {
    case 'config':
        // Return configuration XML
        $xml = '<?xml version="1.0" encoding="UTF-8"?>
<doc>
    <feature>
        <redirect>on</redirect>
        <notneedprofile>on</notneedprofile>
    </feature>
    <param>
        <payment_script>/mancgi/paynet_payment.php</payment_script>
        <result_script>/mancgi/paynet_result.php</result_script>
    </param>
</doc>';
        echo $xml;
        break;
        
    case 'payment':
        // Handle payment processing
        if (!isset($options['payment']) || !isset($options['amount'])) {
            echo '<?xml version="1.0" encoding="UTF-8"?>
<doc><error>Missing payment parameters</error></doc>';
            exit(1);
        }
        
        // Return success - actual payment processing is handled by payment_script
        echo '<?xml version="1.0" encoding="UTF-8"?>
<doc><result>ok</result></doc>';
        break;
        
    case 'recurring':
        // Recurring payments not supported
        echo '<?xml version="1.0" encoding="UTF-8"?>
<doc><result>not_supported</result></doc>';
        break;
        
    default:
        echo '<?xml version="1.0" encoding="UTF-8"?>
<doc><error>unknown command: ' . htmlspecialchars($command) . '</error></doc>';
        exit(1);
}
?>
