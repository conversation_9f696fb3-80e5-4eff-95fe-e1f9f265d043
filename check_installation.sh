#!/bin/bash

echo "Проверка установки модуля Paynet"
echo "================================"
echo ""

# Проверяем файлы
echo "1. Проверка файлов:"
if [ -f "/usr/local/mgr5/addon/pmpaynet.php" ]; then
    echo "   ✓ Модуль: /usr/local/mgr5/addon/pmpaynet.php"
    ls -la /usr/local/mgr5/addon/pmpaynet.php
else
    echo "   ❌ Модуль не найден: /usr/local/mgr5/addon/pmpaynet.php"
fi

if [ -f "/usr/local/mgr5/etc/xml/billmgr_mod_pmpaynet.php.xml" ]; then
    echo "   ✓ XML: /usr/local/mgr5/etc/xml/billmgr_mod_pmpaynet.php.xml"
else
    echo "   ❌ XML не найден: /usr/local/mgr5/etc/xml/billmgr_mod_pmpaynet.php.xml"
fi

echo ""
echo "2. Проверка CGI скриптов:"
for script in paynet_payment.php paynet_result.php paynet_success.php paynet_cancel.php; do
    if [ -f "/usr/local/mgr5/sbin/mancgi/$script" ]; then
        echo "   ✓ $script"
    else
        echo "   ❌ $script не найден"
    fi
done

echo ""
echo "3. Проверка библиотек:"
for lib in bill_func_paynet.php PaynetAPI.php; do
    if [ -f "/usr/local/mgr5/include/php/$lib" ]; then
        echo "   ✓ $lib"
    else
        echo "   ❌ $lib не найден"
    fi
done

echo ""
echo "4. Тестирование модуля:"
if [ -f "/usr/local/mgr5/addon/pmpaynet.php" ]; then
    if /usr/local/mgr5/addon/pmpaynet.php --command=config >/dev/null 2>&1; then
        echo "   ✓ Модуль работает корректно"
    else
        echo "   ❌ Модуль не работает"
        echo "   Вывод модуля:"
        /usr/local/mgr5/addon/pmpaynet.php --command=config 2>&1 | head -10
    fi
fi

echo ""
echo "5. Проверка статуса BILLmanager:"
if pgrep -f billmgr >/dev/null; then
    echo "   ✓ BILLmanager запущен"
else
    echo "   ❌ BILLmanager не запущен"
fi

echo ""
echo "6. Проверка логов:"
if [ -f "/usr/local/mgr5/var/pmpaynet.log" ]; then
    echo "   ✓ Лог файл существует: /usr/local/mgr5/var/pmpaynet.log"
    echo "   Последние записи:"
    tail -5 /usr/local/mgr5/var/pmpaynet.log 2>/dev/null || echo "   (лог пуст)"
else
    echo "   ⚠ Лог файл еще не создан (это нормально до первого использования)"
fi

echo ""
echo "Рекомендации:"
echo "- Если модуль не отображается в веб-интерфейсе, перезапустите BILLmanager"
echo "- Убедитесь, что у файлов правильные права доступа"
echo "- Проверьте логи BILLmanager на наличие ошибок"
echo ""
