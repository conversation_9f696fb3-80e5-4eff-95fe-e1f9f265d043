#!/bin/bash

# Установочный скрипт для модуля Paynet для BILLmanager 6

echo "Установка модуля Paynet для BILLmanager 6..."

# Проверяем, что мы запущены от root
if [ "$EUID" -ne 0 ]; then
    echo "Пожалуйста, запустите скрипт от имени root"
    exit 1
fi

# Проверяем наличие BILLmanager
if [ ! -d "/usr/local/mgr5" ]; then
    echo "BILLmanager не найден в /usr/local/mgr5"
    exit 1
fi

# Проверяем и устанавливаем необходимые PHP пакеты
echo "Проверка необходимых PHP пакетов..."

# Определяем систему управления пакетами
if command -v yum >/dev/null 2>&1; then
    # CentOS/RHEL/AlmaLinux
    echo "Обнаружена система на базе RHEL, используем yum/dnf"
    if command -v dnf >/dev/null 2>&1; then
        PKG_MANAGER="dnf"
    else
        PKG_MANAGER="yum"
    fi

    echo "Установка необходимых пакетов..."
    $PKG_MANAGER install -y php-cli php-common php-xml

elif command -v apt-get >/dev/null 2>&1; then
    # Ubuntu/Debian
    echo "Обнаружена система на базе Debian, используем apt"
    apt-get update
    apt-get install -y php-cli php-common php-xml

else
    echo "Предупреждение: Не удалось определить систему управления пакетами"
    echo "Пожалуйста, установите вручную: php-cli, php-common, php-xml"
    echo "Продолжить установку? (y/n)"
    read -r response
    if [ "$response" != "y" ] && [ "$response" != "Y" ]; then
        exit 1
    fi
fi

# Проверяем, что PHP работает
if ! php -v >/dev/null 2>&1; then
    echo "Ошибка: PHP не работает корректно"
    exit 1
fi

echo "PHP пакеты установлены успешно"

# Создаем необходимые директории
mkdir -p /usr/local/mgr5/var

# Копируем файлы
echo "Копирование файлов..."

# Основной модуль
cp paymethods/pmpaynet.php /usr/local/mgr5/addon/
chmod +x /usr/local/mgr5/addon/pmpaynet.php

# CGI скрипты
cp cgi/*.php /usr/local/mgr5/sbin/mancgi/
chmod +x /usr/local/mgr5/sbin/mancgi/paynet_*.php

# Библиотеки
cp include/php/*.php /usr/local/mgr5/include/php/

# XML конфигурация
cp etc/xml/*.xml /usr/local/mgr5/etc/xml/

echo "Файлы скопированы."

# Проверяем установку
echo "Проверка установленных файлов..."
if [ -f "/usr/local/mgr5/addon/pmpaynet.php" ]; then
    echo "✓ Модуль установлен: /usr/local/mgr5/addon/pmpaynet.php"
else
    echo "❌ Модуль не найден: /usr/local/mgr5/addon/pmpaynet.php"
fi

if [ -f "/usr/local/mgr5/etc/xml/billmgr_mod_pmpaynet.php.xml" ]; then
    echo "✓ XML конфигурация установлена: /usr/local/mgr5/etc/xml/billmgr_mod_pmpaynet.php.xml"
else
    echo "❌ XML конфигурация не найдена: /usr/local/mgr5/etc/xml/billmgr_mod_pmpaynet.php.xml"
fi

# Тестируем модуль
echo "Тестирование модуля..."
if /usr/local/mgr5/addon/pmpaynet.php --command=config >/dev/null 2>&1; then
    echo "✓ Модуль работает корректно"
else
    echo "❌ Модуль не работает"
fi

# Очистка кеша BILLmanager
echo "🔄 Очистка кеша BILLmanager..."
rm -rf /usr/local/mgr5/var/cache/* /usr/local/mgr5/var/tmp/* 2>/dev/null || true

# Перезапускаем BILLmanager правильной командой
echo "🔄 Перезапуск BILLmanager..."
/usr/local/mgr5/sbin/mgrctl exit -m billmgr 2>/dev/null || true

# Ждем запуска BILLmanager
echo "⏳ Ожидание запуска BILLmanager (15 секунд)..."
sleep 15

# Проверяем, что BILLmanager запустился
echo "🔍 Проверка работы BILLmanager..."
if pgrep -f "bin/core billmgr" > /dev/null; then
    echo "✅ BILLmanager успешно запущен"
else
    echo "⚠️  BILLmanager может еще запускаться, проверьте через несколько секунд"
fi

echo "Установка завершена!"
echo ""
echo "ВАЖНО: Если при установке через веб-интерфейс BILLmanager"
echo "возникает ошибка 'binarymodule_result', используйте этот"
echo "скрипт установки - он решает проблему совместимости."
echo ""
echo "Теперь вы можете:"
echo "1. Зайти в административную панель BILLmanager"
echo "2. Перейти в раздел 'Методы оплаты'"
echo "3. Добавить новый метод оплаты 'Paynet'"
echo "4. Настроить параметры подключения к Paynet"
echo ""
echo "Логи модуля записываются в /usr/local/mgr5/var/pmpaynet.log"
