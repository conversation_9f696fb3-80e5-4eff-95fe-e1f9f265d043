#!/usr/bin/php
<?php
echo "Диагностика формы модуля Paynet\n";
echo "===============================\n\n";

// Проверяем XML файл
$xml_file = 'etc/xml/billmgr_mod_pmpaynet.php.xml';
if (!file_exists($xml_file)) {
    echo "❌ XML файл не найден: $xml_file\n";
    exit(1);
}

echo "✓ XML файл найден: $xml_file\n";

// Проверяем валидность XML
$xml = simplexml_load_file($xml_file);
if ($xml === false) {
    echo "❌ XML файл невалиден\n";
    exit(1);
}

echo "✓ XML файл валиден\n";

// Проверяем структуру
$plugin = $xml->plugin;
if (!$plugin) {
    echo "❌ Секция <plugin> не найдена\n";
} else {
    echo "✓ Секция <plugin> найдена: " . $plugin['name'] . "\n";
}

$metadata = $xml->metadata;
if (!$metadata) {
    echo "❌ Секция <metadata> не найдена\n";
} else {
    echo "✓ Секция <metadata> найдена: " . $metadata['name'] . "\n";
}

// Проверяем поля формы
$fields = $xml->xpath('//field');
echo "✓ Найдено полей в форме: " . count($fields) . "\n";

foreach ($fields as $field) {
    $name = (string)$field['name'];
    $input = $field->input;
    $type = (string)$input['type'];
    $required = (string)$input['required'];
    echo "  - $name ($type" . ($required ? ', required' : '') . ")\n";
}

// Проверяем языковые сообщения
$en_messages = $xml->xpath('//lang[@name="en"]//messages[@name="paymethod.edit.pmpaynet.php"]//msg');
$ru_messages = $xml->xpath('//lang[@name="ru"]//messages[@name="paymethod.edit.pmpaynet.php"]//msg');

echo "✓ Английские сообщения: " . count($en_messages) . "\n";
echo "✓ Русские сообщения: " . count($ru_messages) . "\n";

// Проверяем модуль
$module_file = 'paymethods/pmpaynet.php';
if (!file_exists($module_file)) {
    echo "❌ Файл модуля не найден: $module_file\n";
} else {
    echo "✓ Файл модуля найден: $module_file\n";
    
    // Проверяем выполнение команды config
    $output = shell_exec("php $module_file --command=config 2>/dev/null");
    if (strpos($output, '<?xml') === 0) {
        echo "✓ Модуль возвращает валидный XML\n";
    } else {
        echo "❌ Модуль не возвращает валидный XML\n";
        echo "Вывод модуля:\n$output\n";
    }
}

echo "\n";
echo "Рекомендации:\n";
echo "1. Убедитесь, что файлы установлены в правильные директории BILLmanager\n";
echo "2. Перезапустите BILLmanager после установки\n";
echo "3. Проверьте логи BILLmanager на наличие ошибок\n";
echo "4. Убедитесь, что у файлов правильные права доступа\n";

?>
