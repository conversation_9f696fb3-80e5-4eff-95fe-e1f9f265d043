{"info": {"_postman_id": "1102ac21-3acf-4cc1-8932-726e417da0d4", "name": "PSP-PAYNET", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "AUTH", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"token\", jsonData.access_token);", "postman.setEnvironmentVariable(\"ref_token\", jsonData.refresh_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Accept-Language", "value": "ro-RO"}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "password", "type": "text"}, {"key": "username", "value": "601274", "type": "text"}, {"key": "password", "value": "lQUBtknO", "type": "text"}, {"key": "merchantcode", "value": "975860", "type": "text"}, {"key": "salearea", "value": "GeneralTest", "type": "text"}]}, "url": {"raw": "https://api-merchant.test.paynet.md/auth", "protocol": "https", "host": ["api-merchant", "test", "paynet", "md"], "path": ["auth"]}}, "response": []}, {"name": "Payments/Send", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "bearer  VvaygsZjYxRj9br7--hDylD1bT2H4ushtoX0VYV0HeVr_h6urK3M-q7suaE3y4bS3Vtp-pmd1Y85Hme0bcKAwmrwAowIdVgOuiKN9NPL0DsPjC5IrfVFdkqaZMwYQtCZYpoGAU6UqkDXuLMifuAIbKcTRTWcSNOT4UsHzYluVPLR9yKTZUqc8sQ14mi5WMN-0lkHwxOVbiu6IktkdUAChfRyIXyT83QQiQV-7tqR8Srdwf-4IHOlXYwS5ft6bXSB5-8qpoW0ILWmV8HQJRTJEKr7IVyBUIfbkq_QOKDJ-liRNMNL_q7vNvpr0NuIlq46YItnO6rSpRMkz7uLB5Ej9E5crFJEFGjcTPF6Nm3Yb02uRhUBnls8uN9wfRS9DelaeJvvKO1m4oGISPUS8Ws8ZwbAp_gLfghqDIJeQ_WAfBxdWj2QycdpEjksiDk-M36AnJb6n2BwYKuOUWVY9VzqTnakiGNGJFyFLrY5y0OEtXY", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{ \n\t\"Invoice\":214454,\n\t\"MerchantCode\":\"975860\",\n\t\"LinkUrlSuccess\":\"http://localhost:8080/psp/ok?id=1597822954534\",\n\t\"LinkUrlCancel\":\"http://localhost:8080/psp/cancel?id=1597822954534\",\n\t\"Signature\":null,\n\t\"SignVersion\":\"v01\",\n\t\"Customer\":{\n\t\t\"Code\":\"<EMAIL>\",\n\t\t\"Name\":\"Oleg\",\n\t\t\"NameFirst\":\"Stoianov\",\n\t\t\"NameLast\":\"Name last \",\n\t\t\"email\":\"<EMAIL>\",\n\t\t\"Country\":\"Moldova\",\n\t\t\"City\": \"Chisinau\",\n\t\t\"Address\":\"www.paynet.md\",\n\t\t\"PhoneNumber\":\"79000000\"\n\t\t\n\t},\n\t\t\"Payer\":null,\n\t\t\"Currency\":498,\n\t\t\"ExternalDate\":\"2021-09-19T10:42:34\",\n\t\t\"ExpiryDate\":\"2021-09-19T13:42:34\",\n\t\t\"Services\":[\n\t\t\t{\"Name\":\"Demo eshop\",\n\t\t\t\"Description\":\"Demo eShop online desc\",\n\t\t\t\"Amount\":6550,\n\t\t\t\"Products\":[\n\t\t\t\t{\"GroupName\":null,\"QualitiesConcat\":null,\"LineNo\":1,\"GroupId\":null,\"Code\":\"code1001\",\"Barcode\":1001,\"Name\":\"Ticket mini\",\"Description\":\"Description your product MINI\",\"UnitPrice\":2000,\"UnitProduct\":null,\"Quantity\":200,\"Amount\":null,\"Dimensions\":null,\"Qualities\":null,\"TotalAmount\":4000},\n\t\t\t\t{\"GroupName\":null,\"QualitiesConcat\":null,\"LineNo\":2,\"GroupId\":null,\"Code\":\"code1002\",\"Barcode\":1002,\"Name\":\"Ticket MAX\",\"Description\":\"Description your product MAX\",\"UnitPrice\":1050,\"UnitProduct\":null,\"Quantity\":100,\"Amount\":null,\"Dimensions\":null,\"Qualities\":null,\"TotalAmount\":1050},\n\t\t\t\t{\"GroupName\":null,\"QualitiesConcat\":null,\"LineNo\":3,\"GroupId\":null,\"Code\":\"code1003\",\"Barcode\":1003,\"Name\":\"Ticket MAX 3\",\"Description\":\"Description your product MAX\",\"UnitPrice\":500,\"UnitProduct\":null,\"Quantity\":300,\"Amount\":null,\"Dimensions\":null,\"Qualities\":null,\"TotalAmount\":1500}\n\t\t\t\t]\n\t\t\t\t\n\t\t\t}\n\t\t\t],\n\t\t\t\"MoneyType\":null\n\t\n}"}, "url": {"raw": "https://api-merchant.test.paynet.md/api/Payments/Send", "protocol": "https", "host": ["api-merchant", "test", "paynet", "md"], "path": ["api", "Payments", "Send"]}}, "response": []}, {"name": "Redirect", "request": {"method": "POST", "header": [{"key": "f", "value": "", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "operation", "value": "152469", "type": "text"}, {"key": "LinkUrlSucces", "value": "ttp://localhost:8080/psp/ok?id=1597822954534", "type": "text"}, {"key": "LinkUrlCancel", "value": "ttp://localhost:8080/psp/cancel?id=1597822954534", "type": "text"}, {"key": "ExpiryDate", "value": "2020-08-22T21:07:16", "type": "text"}, {"key": "Signature", "value": "bdf479eddca8acf6fefa9a57fdf4f0a2", "type": "text"}, {"key": "<PERSON>", "value": "ru", "type": "text"}]}, "url": {"raw": "https://test.paynet.md/acquiring/getecom?operation=152469&LinkUrlSucces=http://localhost:8080/psp/ok?id=1597822954534&LinkUrlCancel=http://localhost:8080/psp/ok?id=1597822954534&ExpiryDate=2020-08-22T21:07:16&Signature=bdf479eddca8acf6fefa9a57fdf4f0a2&Lang=ru", "protocol": "https", "host": ["test", "paynet", "md"], "path": ["acquiring", "getecom"], "query": [{"key": "operation", "value": "152469"}, {"key": "LinkUrlSucces", "value": "http://localhost:8080/psp/ok?id=1597822954534"}, {"key": "LinkUrlCancel", "value": "http://localhost:8080/psp/ok?id=1597822954534"}, {"key": "ExpiryDate", "value": "2020-08-22T21:07:16"}, {"key": "Signature", "value": "bdf479eddca8acf6fefa9a57fdf4f0a2"}, {"key": "<PERSON>", "value": "ru"}]}}, "response": []}]}