В данном интеграционном пакете вы может найти всё необходимое для интеграция процессинга платежей от компании Paynet.
Для начало вам нужно определить по какой модели будет проходить интеграция
1. Client-Server 
2. Server-Server 
3. Plugin
Если вам не понятны термины Client-Server или Server-Server вы можете по читать нашу техническую документацию
она находится в папке 3. Documentation.
В примерах приведнных в папках Client-Server SDK и Server-Server не настроены линки для нотификаций.
Это означает что после оплаты на ваш сервер не придет нотификация о успешных платежах.
Описание как работают нотификации вы можете найти в папке 3. Documentation  в описание API стр.16-17.

Для проведение тестов по нотификациям, вы можете создать группу в Telegram. 
Назовите группу названием сайта и добавьте туда следующий номер телефона +37379563594.
В чате предоставьте следующею информацию 

ПРИМЕР:
- www.yoursite.md (сайт который вы интегрируите)
- Merchant ID - 123456  (Можете найти чуть ниже )
- Ваш CMS (WP/Magento/Cs.Cart/OpenCart/PrestaShop либо в других случаях API )
- Notification URL http://yoursite.com/payment/paynet/callback   (если у вас интеграция по API)
- Описание вашего вопроса (скриншоты ошибок , примеры неудчных запросов итд.)

После успешного окончание всех тестов , убедитесь что сайт соответствуе требование 
- На сайте перед нажатием кнопки оплатить клиент должен поставить "галочку" о прочтение условии Terms&Conditions 
  (текст вам должны дать в администрации компании владельца сайта)
- На сайте должны быть прописаны адресса и контакты компании а также юр.название компании (обычно где-то внизу в "footer")
- Terms&Conditions и Poltica de confidentialitate тоже должны быть и в footer -е сайта т.е дублируем с тем что у вас стоит перед кнопкой Оплатить.

ИТД ...все требования описаны в 4. Compliance.



При возникновении других вопросов пишите в группу которую создатите в Telegram 


Данные для тестов 

MERCHANT_CODE 		975860
SALE_AREA_CODE          GeneralTest
MERCHANT_SEC_KEY 	5D270BA3-C74D-488C-951A-9D7416A1D11F
MERCHANT_USER 		601274
MERCHANT_USER_PASS 	lQUBtknO


Тестовая банковская карта 
1111 1111 1111 1111 
11/24 
123

