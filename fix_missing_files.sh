#!/bin/bash

echo "Исправление недостающих файлов модуля Paynet"
echo "==========================================="
echo ""

echo "Этот скрипт нужно запустить с правами root:"
echo "sudo ./fix_missing_files.sh"
echo ""

# Проверяем права
if [ "$EUID" -ne 0 ]; then
    echo "❌ Пожалуйста, запустите скрипт от имени root"
    exit 1
fi

# Определяем пути
MGR_PATH="/usr/local/mgr5"
CGI_PATH="$MGR_PATH/sbin/mancgi"

echo "Создание недостающих директорий..."
mkdir -p "$CGI_PATH"

echo "Копирование CGI скриптов..."
cp cgi/*.php "$CGI_PATH/"
chmod +x "$CGI_PATH"/paynet_*.php

echo "Обновление основных файлов..."
cp paymethods/pmpaynet.php "$MGR_PATH/addon/"
cp include/php/*.php "$MGR_PATH/include/php/"
cp etc/xml/*.xml "$MGR_PATH/etc/xml/"

echo ""
echo "Проверка установленных файлов:"

FILES_TO_CHECK=(
    "$MGR_PATH/addon/pmpaynet.php"
    "$MGR_PATH/etc/xml/billmgr_mod_pmpaynet.php.xml"
    "$CGI_PATH/paynet_payment.php"
    "$CGI_PATH/paynet_result.php"
    "$CGI_PATH/paynet_success.php"
    "$CGI_PATH/paynet_cancel.php"
    "$MGR_PATH/include/php/bill_func_paynet.php"
    "$MGR_PATH/include/php/PaynetAPI.php"
)

ALL_OK=true
for file in "${FILES_TO_CHECK[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file"
    else
        echo "❌ $file"
        ALL_OK=false
    fi
done

if [ "$ALL_OK" = true ]; then
    echo ""
    echo "✅ Все файлы установлены успешно!"
    echo ""
    echo "Теперь:"
    echo "1. Перезапустите BILLmanager"
    echo "2. Форма настройки должна отображаться"
    echo "3. Заполните настройки Paynet"
else
    echo ""
    echo "❌ Некоторые файлы не установлены"
fi

echo ""
echo "Для проверки запустите: ./check_installation.sh"
