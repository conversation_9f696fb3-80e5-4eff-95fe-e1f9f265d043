# Платежный модуль Paynet для BILLmanager 6

## Описание
Модуль для интеграции платежной системы Paynet с BILLmanager 6.

## Требования

Для работы модуля требуются пакеты:
- `php-cli`
- `php-common`
- `php-xml`

## Установка

### Метод 1: Автоматическая установка (рекомендуемый)

Скрипт автоматически установит необходимые PHP пакеты:
```bash
sudo ./install.sh
```

### Метод 2: Ручная установка

1. Установите необходимые PHP пакеты:

   **CentOS/RHEL/AlmaLinux:**
   ```bash
   sudo yum install -y php-cli php-common php-xml
   # или
   sudo dnf install -y php-cli php-common php-xml
   ```

   **Ubuntu/Debian:**
   ```bash
   sudo apt-get update
   sudo apt-get install -y php-cli php-common php-xml
   ```

2. Скопируйте файлы в соответствующие папки BILLmanager:
   ```bash
   sudo cp paymethods/pmpaynet.php /usr/local/mgr5/addon/
   sudo cp cgi/*.php /usr/local/mgr5/sbin/mancgi/
   sudo cp include/php/*.php /usr/local/mgr5/include/php/
   sudo cp etc/xml/*.xml /usr/local/mgr5/etc/xml/
   ```

3. Установите права на файлы:
   ```bash
   sudo chmod +x /usr/local/mgr5/addon/pmpaynet.php
   sudo chmod +x /usr/local/mgr5/sbin/mancgi/paynet_*.php
   ```

4. Перезапустите BILLmanager:
   ```bash
   sudo /usr/local/mgr5/sbin/mgrctl -m billmgr restart
   ```

### Решение проблем с установкой

**Ошибка "binarymodule_result":**

Эта ошибка обычно возникает из-за отсутствия необходимых PHP пакетов.

**Проверка системы:**
```bash
php check_php.php
```

**Решение:**
1. Установите необходимые пакеты: `php-cli`, `php-common`, `php-xml`
2. Используйте автоматический скрипт установки вместо веб-интерфейса
3. Перезапустите BILLmanager после установки

## Настройка

В административной панели BILLmanager создайте новый метод оплаты и укажите:

- **Merchant Code**: Код мерчанта в Paynet
- **Merchant Secret Key**: Секретный ключ мерчанта
- **Merchant User**: Пользователь API
- **Merchant User Password**: Пароль пользователя API
- **Test Mode**: Отметьте для тестового режима

## Настройка callback URL

В личном кабинете Paynet укажите callback URL:
```
http://your-domain.com/mancgi/paynet_result.php
```

## Тестовые данные

Для тестирования используйте:

- **MERCHANT_CODE**: 975860
- **MERCHANT_SEC_KEY**: 5D270BA3-C74D-488C-951A-9D7416A1D11F
- **MERCHANT_USER**: 601274
- **MERCHANT_USER_PASS**: lQUBtknO

Тестовая карта:
- Номер: 1111 1111 1111 1111
- Срок: 11/24
- CVV: 123

## Особенности

- Модуль использует Client-Server модель Paynet
- Поддерживает автоматическое подтверждение платежей через callback
- Суммы передаются в копейках (умножение на 100)
- Логи записываются в `/usr/local/mgr5/var/pmpaynet.log`


В случае интеграции  Wordpress , OpenCart v.3, CS-Cart ,Magento и Prestashop у нас есть плагины
Если у вас другой CMS тогда интеграция будет посредством API.

API или плагин вы можете скачать тут https://paynet.md/merchant/#cmsmodules

Для начало тестов вам нужен будет тестовый аккаунт, можете его зарегистрировать тут
https://test.paynet.md/merchant/user/signup
после регистрации (Не нужно создавать никаких паролей) , скиньте нам в чат в Telegram следующую информацию

- Ссылку на ваш сайт
- Ваш партнер Ид (который вам выдаст наша система )
- Название вашего CMS (Wordpress, Opencart , Cs-cart итд.  если вы интернируйтесь по API просто напишите API).
- Также в случае интеграции по API нам необходим будет ваш URL для нотификации (Детальную информацию можете прочесть  на 16-й странице скаченной вами документации).
- В случае плагинов URL для нотификации не нужен

После получения от вас выше указанной информации мы вам вышлем на почту профиль с данными для тестов.


Пример ответа после регистрации тестового аккаунта
- www.yoursite.md
- Partner ID - 123456 (из полученного на почту письма)
- Название CMS (Wordpress , OpenCart v.3, CS-Cart ,Magento или Prestashop если у вас другой CMS просто напишите API)
- В случае если интеграция по API напишите ваш (к примеру Notification URL http://yoursite.com/payment/paynet/callback )

ВАЖНО: Перед переходом на Live убедитесь что сайт соответствует требованиям (в противном случае мы не сможем запустить Live)
- На сайте перед нажатием кнопку оплатить клиент должен поставить "галочку" о прочтение условии Terms&Conditions (текст вам должны дать в администрации компании владельца сайта)
- На сайте в Footer должны быть прописаны:Юр.название , адреса и контакты компании Тел. email итд.
- Также в "Footer" ОБЯЗАТЕЛЬНО ссылки на Terms&Conditions ,Privacy policy , Логотипы систем оплаты Visa&Mastercard , Paynet .


данные которые использовал -
MERCHANT_CODE     975860
SALE_AREA_CODE          GeneralTest
MERCHANT_SEC_KEY   5D270BA3-C74D-488C-951A-9D7416A1D11F
MERCHANT_USER     601274
MERCHANT_USER_PASS   lQUBtknO
