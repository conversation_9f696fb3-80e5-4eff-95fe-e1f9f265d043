Платежный модуль Paynet для BILLmanager 6


В случае интеграции  Wordpress , OpenCart v.3, CS-Cart ,Magento и Prestashop у нас есть плагины
Если у вас другой CMS тогда интеграция будет посредством API.

API или плагин вы можете скачать тут https://paynet.md/merchant/#cmsmodules

Для начало тестов вам нужен будет тестовый аккаунт, можете его зарегистрировать тут
https://test.paynet.md/merchant/user/signup
после регистрации (Не нужно создавать никаких паролей) , скиньте нам в чат в Telegram следующую информацию

- Ссылку на ваш сайт
- Ваш партнер Ид (который вам выдаст наша система )
- Название вашего CMS (Wordpress, Opencart , Cs-cart итд.  если вы интернируйтесь по API просто напишите API).
- Также в случае интеграции по API нам необходим будет ваш URL для нотификации (Детальную информацию можете прочесть  на 16-й странице скаченной вами документации).
- В случае плагинов URL для нотификации не нужен

После получения от вас выше указанной информации мы вам вышлем на почту профиль с данными для тестов.


Пример ответа после регистрации тестового аккаунта
- www.yoursite.md
- Partner ID - 123456 (из полученного на почту письма)
- Название CMS (Wordpress , OpenCart v.3, CS-Cart ,Magento или Prestashop если у вас другой CMS просто напишите API)
- В случае если интеграция по API напишите ваш (к примеру Notification URL http://yoursite.com/payment/paynet/callback )

ВАЖНО: Перед переходом на Live убедитесь что сайт соответствует требованиям (в противном случае мы не сможем запустить Live)
- На сайте перед нажатием кнопку оплатить клиент должен поставить "галочку" о прочтение условии Terms&Conditions (текст вам должны дать в администрации компании владельца сайта)
- На сайте в Footer должны быть прописаны:Юр.название , адреса и контакты компании Тел. email итд.
- Также в "Footer" ОБЯЗАТЕЛЬНО ссылки на Terms&Conditions ,Privacy policy , Логотипы систем оплаты Visa&Mastercard , Paynet .


данные которые использовал -
MERCHANT_CODE     975860
SALE_AREA_CODE          GeneralTest
MERCHANT_SEC_KEY   5D270BA3-C74D-488C-951A-9D7416A1D11F
MERCHANT_USER     601274
MERCHANT_USER_PASS   lQUBtknO
